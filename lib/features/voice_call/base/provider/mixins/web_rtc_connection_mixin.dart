import 'dart:async';

import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/utils/tracking_permission_utils.dart';
import 'package:flutter_audio_room/features/instant_chat/data/model/coturn_config_bo_model.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_base.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

/// WebRTC常量配置
const kRtcConfig = {
  "iceServers": [],
  'sdpSemantics': 'unified-plan',
  'encodedInsertableStreams': true,
};

/// 处理 WebRTC 连接相关的逻辑
mixin WebRTCConnectionMixin on VoiceCallBaseMixin {
  Timer? _reconnectTimer;
  CoturnConfigBOModel? _lastCoturnConfig;

  /// Check if error is recoverable for reconnection
  bool _isRecoverableError(dynamic error) {
    if (error == null) return true;

    final errorMessage = error.toString().toLowerCase();

    // Non-recoverable errors
    final nonRecoverableErrors = [
      'permission denied',
      'device not available',
      'device not found',
      'access denied',
      'unauthorized',
      'forbidden',
      'invalid credentials',
      'authentication failed',
    ];

    for (final nonRecoverableError in nonRecoverableErrors) {
      if (errorMessage.contains(nonRecoverableError)) {
        return false;
      }
    }

    return true;
  }

  /// Calculate exponential backoff delay
  int _calculateReconnectDelay(int attemptCount) {
    // Exponential backoff: 1s, 2s, 4s
    return (1 << (attemptCount - 1)).clamp(1, 4);
  }

  /// Start reconnection process
  Future<void> _startReconnection() async {
    if (state.isReconnecting || _lastCoturnConfig == null) {
      return;
    }

    final currentAttempts = state.reconnectAttempts + 1;

    if (currentAttempts > state.maxReconnectAttempts) {
      LogUtils.e('Max reconnect attempts reached, disconnecting',
          tag: 'WebRTC');
      _finalizeDisconnection(CallDisconnectReason.error);
      return;
    }

    LogUtils.i(
        'Starting reconnection attempt $currentAttempts/${state.maxReconnectAttempts}',
        tag: 'WebRTC');

    state = state.copyWith(
      isReconnecting: true,
      reconnectAttempts: currentAttempts,
      callStatus: CallReconnecting(
        attemptCount: currentAttempts,
        maxAttempts: state.maxReconnectAttempts,
      ),
    );

    final delay = _calculateReconnectDelay(currentAttempts);
    LogUtils.d('Reconnecting in ${delay}s...', tag: 'WebRTC');

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(Duration(seconds: delay), () async {
      await _attemptReconnection();
    });
  }

  /// Attempt to reconnect
  Future<void> _attemptReconnection() async {
    if (!state.isReconnecting || _lastCoturnConfig == null) {
      return;
    }

    LogUtils.i(
        'Attempting reconnection ${state.reconnectAttempts}/${state.maxReconnectAttempts}',
        tag: 'WebRTC');

    // Clean up existing connection
    await _cleanupForReconnection();

    // Attempt to create new connection
    // Note: createPeerConnection will handle errors internally and may trigger another reconnection
    await createPeerConnection(_lastCoturnConfig!);

    // The success/failure will be handled by the connection state callbacks
    // If connection succeeds, _handleConnectStateChanged will reset reconnection state
    // If connection fails, it will either trigger another reconnection or finalize disconnection
  }

  /// Clean up resources for reconnection
  Future<void> _cleanupForReconnection() async {
    // Don't dispose timers or stop ringtone during reconnection
    if (state.peerConnection != null) {
      await state.peerConnection!.close();
      state = state.copyWith(peerConnection: null);
    }
  }

  /// Finalize disconnection after all reconnection attempts failed
  void _finalizeDisconnection(CallDisconnectReason reason) {
    _reconnectTimer?.cancel();
    disposeWebRTC();
    disposeTimer();
    state = state.copyWith(
      callStatus: CallDisconnected(reason),
      isReconnecting: false,
      reconnectAttempts: 0,
    );
    stopRingtone();
  }

  /// Clean up reconnection resources
  @protected
  void disposeReconnection() {
    _reconnectTimer?.cancel();
    _lastCoturnConfig = null;

    // Reset reconnection state
    state = state.copyWith(
      isReconnecting: false,
      reconnectAttempts: 0,
    );
  }
  Future<void> setupPeerConnection() async {
    state.peerConnection!
      ..onIceCandidate = _sendIceCandidate
      ..onConnectionState = _handleConnectStateChanged
      ..onIceConnectionState = _handleIceConnectionState
      ..onTrack = _handleRemoteTrack
      ..onDataChannel = handleDataChannel;

    // 如果是发起方，需要创建数据通道
    if (state.isCaller) {
      await setupDataChannel(state.peerConnection!);
    }
  }

  @override
  Future<void> createPeerConnection(CoturnConfigBOModel coturnConfigBO) async {
    // Save config for potential reconnection
    _lastCoturnConfig = coturnConfigBO;

    final turnConfigs = coturnConfigBO.turnConfigs ?? [];
    final stunConfigs = coturnConfigBO.stunConfigs ?? [];

    // Create ICE server configurations
    final List<Map<String, dynamic>> iceServers = [];

    // Add STUN servers
    for (final stunConfig in stunConfigs) {
      if (stunConfig.isEmpty) continue;
      final stunUrl =
          'stun:${stunConfig.stunServerHost}:${stunConfig.stunServerPort}';
      iceServers.add({
        'urls': [stunUrl],
      });
    }

    // Add TURN servers
    for (final turnConfig in turnConfigs) {
      final turnUrl = 'turn:${turnConfig.serverHost}:${turnConfig.serverPort}';
      iceServers.add({
        'urls': [turnUrl],
        'username': turnConfig.username,
        'credential': turnConfig.password,
      });
    }

    final config = {
      ...kRtcConfig,
      'iceServers': iceServers,
    };

    final result = await webRtcService.createPeerConnection(config);

    await result.fold(
      (error) async {
        LogUtils.e('Failed to create peer connection: ${error.message}',
            tag: 'WebRTC');

        final underlyingError = error is RTCException ? error.error : error;
        if (_isRecoverableError(underlyingError) && !state.isReconnecting) {
          await _startReconnection();
        } else if (state.isReconnecting) {
          // During reconnection, don't call handleError which might change the call status
          // Let the reconnection process handle the failure
          LogUtils.w(
              'Connection creation failed during reconnection, will retry if attempts remain',
              tag: 'WebRTC');
        } else {
          handleError(error.message);
        }
      },
      (peerConnection) async {
        // Reset reconnection state on successful connection
        state = state.copyWith(
          peerConnection: peerConnection,
          callStatus: CallConnecting(),
          reconnectAttempts: 0,
          isReconnecting: false,
        );
        await setupLocalStream(state.peerConnection!);
        await setupPeerConnection();
      },
    );
  }

  void _handleConnectStateChanged(RTCPeerConnectionState connectionState) {
    LogUtils.d('Connection state changed: $connectionState', tag: 'WebRTC');
    if (connectionState ==
        RTCPeerConnectionState.RTCPeerConnectionStateConnected) {
      if (state.callStatus is! CallConnected) {
        _handleConnected();

        _startDurationTimer();

        // Reset reconnection state on successful connection
        state = state.copyWith(
          callStatus: CallConnected(),
          reconnectAttempts: 0,
          isReconnecting: false,
        );

        // Track voice call connected - determine call type based on context
        final isInstantCall = state.callId?.startsWith('instant') ?? false;
        if (isInstantCall) {
          TrackingPermissionUtils.trackInstantVoiceCall(
            eventType: 'call_connected',
            matchId: state.callId,
            peerId: state.peerId,
            isEncrypted: state.isAudioEncryptEnabled,
            success: true,
          );
        } else {
          TrackingPermissionUtils.trackFriendVoiceCall(
            eventType: 'call_connected',
            callId: state.callId,
            friendId: state.peerId,
            friendName: state.peerNickName,
            isEncrypted: state.isAudioEncryptEnabled,
            isCaller: state.isCaller,
            success: true,
          );
        }
      }
    } else if (connectionState ==
        RTCPeerConnectionState.RTCPeerConnectionStateFailed) {
      // Handle connection failure with reconnection logic
      LogUtils.e('WebRTC connection failed', tag: 'WebRTC');

      if (!state.isReconnecting && _lastCoturnConfig != null) {
        LogUtils.i('Connection failed, attempting reconnection', tag: 'WebRTC');
        _startReconnection();
      } else if (state.isReconnecting) {
        LogUtils.w(
            'Connection failed during reconnection, will retry in next attempt',
            tag: 'WebRTC');
        // Don't finalize disconnection here, let the reconnection process handle it
      } else {
        LogUtils.e('Connection failed and no reconnection config available',
            tag: 'WebRTC');
        _finalizeDisconnection(CallDisconnectReason.error);
      }
    }
  }

  void _handleIceConnectionState(RTCIceConnectionState iceState) {
    LogUtils.d('ICE connection state changed: $iceState', tag: 'WebRTC');
    EasyThrottle.throttle(
        'iceConnectionState', const Duration(milliseconds: 300), () {
      if (iceState == RTCIceConnectionState.RTCIceConnectionStateDisconnected) {
        LogUtils.w('ICE connection disconnected', tag: 'WebRTC');

        if (!state.isReconnecting && _lastCoturnConfig != null) {
          LogUtils.i('ICE disconnected, attempting reconnection',
              tag: 'WebRTC');
          _startReconnection();
        } else if (state.isReconnecting) {
          LogUtils.w(
              'ICE disconnected during reconnection, will retry in next attempt',
              tag: 'WebRTC');
          // Don't finalize disconnection here, let the reconnection process handle it
        } else {
          LogUtils.e('ICE disconnected and no reconnection config available',
              tag: 'WebRTC');
          _finalizeDisconnection(CallDisconnectReason.peerLeave);
        }
      } else if (iceState ==
          RTCIceConnectionState.RTCIceConnectionStateFailed) {
        LogUtils.e('ICE connection failed', tag: 'WebRTC');

        if (!state.isReconnecting && _lastCoturnConfig != null) {
          LogUtils.i('ICE failed, attempting reconnection', tag: 'WebRTC');
          _startReconnection();
        } else if (state.isReconnecting) {
          LogUtils.w(
              'ICE failed during reconnection, will retry in next attempt',
              tag: 'WebRTC');
          // Don't finalize disconnection here, let the reconnection process handle it
        } else {
          LogUtils.e('ICE failed and no reconnection config available',
              tag: 'WebRTC');
          _finalizeDisconnection(CallDisconnectReason.error);
        }
      }
    });
  }

  void _handleRemoteTrack(RTCTrackEvent event) async {
    stopRingtone();
    LogUtils.d('Remote track event: ${event.track.kind}', tag: 'WebRTC');
    if (event.track.kind == 'audio') {
      final stream = event.streams.first;

      stream.getAudioTracks().forEach((track) {
        track.enabled = false;
      });

      state = state.copyWith(
        remoteStream: stream,
      );

      // 初始化音频设备
      await initializeAudioDevices();
    }
  }

  Future<void> _handleConnected() async {
    var connectType = '';
    var protocol = '';

    if (state.peerConnection != null) {
      final stats = await state.peerConnection!.getStats();
      for (var report in stats) {
        if (report.type == 'local-candidate') {
          connectType = report.values['candidateType'] ?? '';
          protocol = report.values['protocol'] ?? '';
          break;
        }
      }
    }

    LogUtils.d('Connection established: $connectType/$protocol', tag: 'WebRTC');

    await sendConnectedMessage(connectType, protocol);
  }

  void _startDurationTimer() {
    durationTimer?.cancel();
    state = state.copyWith(duration: Duration.zero);
    durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      state = state.copyWith(duration: Duration(seconds: timer.tick));
    });
  }

  Future<void> _sendIceCandidate(RTCIceCandidate candidate) async {
    await sendIceCandidate(candidate);
  }

  @override
  Future<void> handleOfferMessage(
    Map<String, dynamic> message,
  ) async {
    final encryptedSessionKey = message['sessionKey'] as String?;
    if (encryptedSessionKey == null) {
      throw Exception('Encrypted session key is null');
    }

    final decryptedResult = cryptoService.decryptSessionKeyForUser(
      encryptedKey: encryptedSessionKey,
    );
    decryptedResult.fold(
      (error) => handleError(error.message),
      (key) => sessionKey = key,
    );

    await _handleSdpMessage(message);

    await setupEncryption();
    await toggleAudioEncryption(true);
    await toggleAudioDecryption(true);

    enableAudioTracks();

    await _createAndSendAnswer();
  }

  @override
  Future<void> handleAnswerMessage(
    Map<String, dynamic> message,
  ) async {
    await _handleSdpMessage(message);

    // await setupEncryption();
    // await toggleAudioEncryption(true);
    await toggleAudioDecryption(true);

    enableAudioTracks();
  }

  Future<void> _handleSdpMessage(Map<String, dynamic> message) async {
    if (state.peerConnection == null) {
      LogUtils.d('PeerConnection is null when handling SDP message',
          tag: 'WebRTC');
      return;
    }

    final sdp = RTCSessionDescription(
      message['sdp']['desc'],
      message['sdp']['type'],
    );

    final rs =
        await webRtcService.setRemoteDescription(state.peerConnection!, sdp);
    if (rs.isLeft()) {
      LogUtils.e('Failed to set remote description',
          tag: 'WebRTC', error: rs.fold((l) => l, (r) => null));
      return;
    }
    hasSetRemoteDescription = true;
    for (final candidate in iceCandidatesToAdd) {
      await webRtcService.addIceCandidate(state.peerConnection!, candidate);
    }
    iceCandidatesToAdd.clear();
  }

  Future<void> _createAndSendAnswer() async {
    final result = await webRtcService.createAnswer(state.peerConnection!);

    result.fold(
      (error) => state = state.copyWith(
        callStatus: CallError(error.message),
      ),
      (answer) async {
        await webRtcService.setLocalDescription(state.peerConnection!, answer);
        await sendAnswerMessage(answer);
      },
    );
  }

  @override
  Future<void> createOffer() async {
    if (state.peerConnection == null) {
      LogUtils.w('PeerConnection is null when creating offer', tag: 'WebRTC');
      return;
    }

    await setupEncryption();
    await toggleAudioEncryption(true);

    final result = await webRtcService.createOffer(state.peerConnection!);

    await result.fold(
      (error) async => state = state.copyWith(
        callStatus: CallError(error.message),
      ),
      (offer) async {
        await webRtcService.setLocalDescription(state.peerConnection!, offer);
        await sendOfferMessage(offer);
      },
    );
  }
}
