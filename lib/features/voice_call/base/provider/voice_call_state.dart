import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_gift.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/encryption_state.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_call_state.freezed.dart';

@freezed
class VoiceCallState with _$VoiceCallState {
  const factory VoiceCallState({
    required String myUserId,
    @Default(MatchNotStarted()) MatchStatus matchStatus,
    required CallStatus callStatus,
    @Default(null) String? callId,
    @Default(null) String? peerId,
    @Default(null) String? peerNickName,
    @Default({}) Map<String, dynamic> peerAvatar,
    @Default(false) bool isCaller,
    RoomMessageGift? lastGiftInfo,
    @Default(Duration.zero) Duration duration,
    @Default(EncryptionState.notInitialized) EncryptionState encryptionState,
    @Default({}) Map<String, int> cryptorStates,
    @Default(false) bool isAudioEncryptEnabled,
    @Default(false) bool isAudioDecryptEnabled,
    RTCPeerConnection? peerConnection,
    MediaStream? localStream,
    MediaStream? remoteStream,
    @Default(0) int reconnectAttempts,
    @Default(3) int maxReconnectAttempts,
    @Default(false) bool isReconnecting,
  }) = _VoiceCallState;

  const VoiceCallState._();

  bool get isCallActive =>
      callStatus is CallConnecting || callStatus is CallConnected;

  bool get isFullyEncrypted => cryptorStates.values
      .every((state) => state == 1); // 1 represents OK state
}
