import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/widgets/audio_player/audio_player_controller.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/callback_handlers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/audio_stream_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/data_channel_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/encryption_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/ringtone_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/web_rtc_connection_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_base.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_state.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/mixins/friend_web_socket_mixin.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/session_key_service/i_session_key_service.dart';
import 'package:flutter_audio_room/services/web_rtc_service/i_web_rtc_service.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'friend_voice_call_provider.g.dart';

@riverpod
class FriendVoiceCall extends _$FriendVoiceCall
    with
        VoiceCallBaseMixin,
        AudioStreamMixin,
        WebRTCConnectionMixin,
        DataChannelMixin,
        EncryptionMixin,
        FriendWebSocketMixin,
        RingtoneMixin {
  CallbackQueue? _callbackQueue;

  @override
  VoiceCallState build() {
    audioPlayerController = ref.watch(audioPlayerControllerProvider.notifier);
    final myUserId = ref.read(accountProvider).userInfo?.profile?.id;
    if (myUserId == null) {
      handleError('User info not found');
      return VoiceCallState(
        myUserId: '',
        callStatus: CallError('User info not found'),
      );
    }

    _callbackQueue = CallbackQueue();

    ref.onDispose(() {
      dispose();
    });

    return VoiceCallState(
      myUserId: myUserId,
      callStatus: CallNotStarted(),
    );
  }

  @override
  CallbackQueue? get callbackQueue => _callbackQueue;

  // CallBaseMixin 实现
  @override
  final List<RTCRtpSender> senders = [];

  @override
  final Map<String, FrameCryptor> frameCryptors = {};

  @override
  final Map<String, int> cryptorRetryCount = {};

  @override
  IWebRtcService get webRtcService => getIt<IWebRtcService>();

  @override
  ISessionKeyService get sessionKeyService => getIt<ISessionKeyService>();

  @override
  ICryptoService get cryptoService => getIt<ICryptoService>();

  // Error handling
  @override
  @protected
  void handleError(String message) {
    state = state.copyWith(
      callStatus: CallError(message),
    );
  }

  @override
  void dispose() async {
    stopRingtone();
    await disposeWebRTC();
    disposeTimer();
    _disposeEncryption();
    await disposeWebSocket();
    _callbackQueue?.clear();
  }

  @override
  @protected
  Future<void> disposeWebRTC() async {
    try {
      // Clean up reconnection resources first
      disposeReconnection();

      // 停止所有音频轨道 - 使用List.from创建副本避免并发修改
      final localTracks = List<MediaStreamTrack>.from(
          state.localStream?.getAudioTracks() ?? []);
      for (final track in localTracks) {
        track.enabled = false;
        await track.stop();
      }

      final remoteTracks =
          List<MediaStreamTrack>.from(
          state.remoteStream?.getAudioTracks() ?? []);
      for (final track in remoteTracks) {
        track.enabled = false;
        await track.stop();
      }

      // 关闭流
      await state.localStream?.dispose();
      await state.remoteStream?.dispose();

      // 关闭 DataChannel
      cleanUpDataChannel();

      // 关闭 PeerConnection
      await state.peerConnection?.close();

      state = state.copyWith(
        localStream: null,
        remoteStream: null,
        peerConnection: null,
      );
    } catch (e) {
      debugPrint('Error in disposeWebRTC: $e');
    }
  }

  /// 清理定时器相关资源
  @protected
  @override
  void disposeTimer() {
    durationTimer?.cancel();
    keyUpdateTimer?.cancel();
    keyUpdateTimer = null;
    durationTimer = null;
    disposeCallTimer(); // 清理通话定时器
  }

  /// 清理加密相关资源
  @protected
  void _disposeEncryption() {
    try {
      // 清理加密器 - 使用List.from创建副本避免并发修改
      final cryptorsCopy = List<FrameCryptor>.from(frameCryptors.values);
      for (var cryptor in cryptorsCopy) {
        cryptor.dispose();
      }
      frameCryptors.clear();
      cryptorRetryCount.clear();

      // 清理密钥提供者
      keyProvider?.dispose();
      keyProvider = null;
      sessionKey = null;
    } catch (e) {
      debugPrint('Error in _disposeEncryption: $e');
    }
  }
}
