sealed class CallStatus {}

class CallNotStarted extends CallStatus {}

class WaitingForAnswer extends CallStatus {}

class CallIncoming extends CallStatus {}

class CallConnecting extends CallStatus {}

class CallConnected extends CallStatus {}

class CallReconnecting extends CallStatus {
  final int attemptCount;
  final int maxAttempts;
  CallReconnecting({required this.attemptCount, required this.maxAttempts});
}

class CallError extends CallStatus {
  final String message;
  CallError(this.message);
}

enum CallDisconnectReason {
  manualLeave,
  peerLeave,
  cancel,
  hangup,
  error,
}

class CallDisconnected extends CallStatus {
  final CallDisconnectReason reason;
  CallDisconnected(this.reason);
}
